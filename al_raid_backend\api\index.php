<?php
// Enhanced CORS headers for production
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, Accept");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Error reporting for debugging (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/database.php';

// Enhanced path parsing to handle different server configurations
$method = $_SERVER['REQUEST_METHOD'];
$requestUri = $_SERVER['REQUEST_URI'];
$scriptName = $_SERVER['SCRIPT_NAME'];

// Remove script name from request URI to get the path
$path = str_replace(dirname($scriptName), '', $requestUri);
$path = str_replace('/index.php', '', $path);

// Remove query string
$path = parse_url($path, PHP_URL_PATH);

// Remove leading slash
$path = ltrim($path, '/');

// Split path into segments
$segments = explode('/', $path);
$endpoint = $segments[0] ?? '';

// Log the request for debugging
error_log("API Request - Method: $method, Path: $path, Endpoint: $endpoint");

// Database connection with error handling
$database = new Database();
$db = $database->getConnection();

if (!$db) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed',
        'debug' => 'Could not connect to database'
    ]);
    exit();
}

// Response helper function
function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit();
}

// Error handler
function sendError($message, $status = 400, $debug = null) {
    $response = ['success' => false, 'message' => $message];
    if ($debug) {
        $response['debug'] = $debug;
    }
    sendResponse($response, $status);
}

// File upload helper
function uploadFile($file, $uploadDir) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return null;
    }

    // Ensure upload directory exists
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            throw new Exception('Failed to create upload directory.');
        }
    }

    // Check file type using both MIME type and file extension
    $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    $allowedExtensions = ['jpg', 'jpeg', 'png'];

    // Get file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    // Validate extension
    if (!in_array($extension, $allowedExtensions)) {
        throw new Exception('Invalid file extension. Only JPG, JPEG and PNG are allowed.');
    }

    // Validate MIME type (if available)
    if (!empty($file['type']) && !in_array($file['type'], $allowedMimeTypes)) {
        throw new Exception('Invalid file type. Only JPEG and PNG are allowed.');
    }

    // Additional check using finfo if available
    if (function_exists('finfo_open') && file_exists($file['tmp_name'])) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detectedType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($detectedType, $allowedMimeTypes)) {
            throw new Exception('Invalid file content. Only JPEG and PNG images are allowed.');
        }
    }

    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        throw new Exception('File size too large. Maximum 5MB allowed.');
    }

    $filename = uniqid() . '.' . $extension;
    $uploadPath = $uploadDir . $filename;

    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        throw new Exception('Failed to upload file.');
    }

    return $filename;
}

try {
    switch ($endpoint) {
        case '':
        case 'health':
            // Health check endpoint
            sendResponse([
                'success' => true,
                'message' => 'API is working',
                'timestamp' => date('Y-m-d H:i:s'),
                'method' => $method,
                'path' => $path,
                'endpoint' => $endpoint
            ]);
            break;

        case 'register':
            if ($method !== 'POST') {
                sendError('Method not allowed', 405);
            }

            $fullName = $_POST['full_name'] ?? '';
            $email = $_POST['email'] ?? '';
            $phoneNumber = $_POST['phone_number'] ?? '';
            $shippingAddress = $_POST['shipping_address'] ?? '';
            $country = $_POST['country'] ?? '';
            $role = $_POST['role'] ?? '';
            $password = $_POST['password'] ?? '';

            // Validate required fields
            if (empty($fullName) || empty($email) || empty($phoneNumber) ||
                empty($shippingAddress) || empty($country) || empty($role) || empty($password)) {
                sendError('All fields are required');
            }

            // Validate email format
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                sendError('Invalid email format');
            }

            // Validate password strength
            if (strlen($password) < 6) {
                sendError('Password must be at least 6 characters long');
            }

            // Validate role
            if (!in_array($role, ['merchant', 'buyer'])) {
                sendError('Invalid role. Must be either merchant or buyer');
            }

            // Validate phone number format (basic validation)
            if (!preg_match('/^[\+]?[0-9\-\(\)\s]+$/', $phoneNumber)) {
                sendError('Invalid phone number format');
            }

            // Check if email already exists
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                sendError('Email already exists');
            }

            // Hash password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Handle commercial register upload
            $commercialRegisterPhoto = null;
            if (isset($_FILES['commercial_register_photo'])) {
                $commercialRegisterPhoto = uploadFile($_FILES['commercial_register_photo'], '../uploads/commercial_registers/');
            }

            // Insert user
            $stmt = $db->prepare("
                INSERT INTO users (full_name, email, phone_number, password, commercial_register_photo, shipping_address, country, role)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");

            if ($stmt->execute([$fullName, $email, $phoneNumber, $hashedPassword, $commercialRegisterPhoto, $shippingAddress, $country, $role])) {
                $userId = $db->lastInsertId();

                // Get the created user
                $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                sendResponse([
                    'success' => true,
                    'message' => 'User registered successfully',
                    'user' => $user
                ]);
            } else {
                sendError('Registration failed');
            }
            break;

        case 'login':
            if ($method !== 'POST') {
                sendError('Method not allowed', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true);
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';

            // Validate required fields
            if (empty($email) || empty($password)) {
                sendError('Email and password are required');
            }

            // Validate email format
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                sendError('Invalid email format');
            }

            // Get user by email
            $stmt = $db->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                sendError('Invalid email or password');
            }

            // Verify password
            if (!password_verify($password, $user['password'])) {
                sendError('Invalid email or password');
            }

            // Remove password from response for security
            unset($user['password']);

            sendResponse([
                'success' => true,
                'message' => 'Login successful',
                'user' => $user
            ]);
            break;

        case 'user':
            if ($method !== 'GET') {
                sendError('Method not allowed', 405);
            }

            $userId = $segments[1] ?? '';
            if (empty($userId)) {
                sendError('User ID is required');
            }

            $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user) {
                sendResponse([
                    'success' => true,
                    'user' => $user
                ]);
            } else {
                sendError('User not found', 404);
            }
            break;

        case 'products':
            if ($method === 'GET') {
                try {
                    $type = $_GET['type'] ?? '';
                    $userId = $_GET['user_id'] ?? '';

                    // Validate type parameter
                    if (!empty($type) && !in_array($type, ['industrial', 'food'])) {
                        sendError('Invalid product type. Must be "industrial" or "food"', 400);
                    }

                    if (!empty($userId)) {
                        // Get products for specific merchant (including all statuses)
                        $sql = "SELECT p.*, u.full_name as merchant_name FROM products p
                                LEFT JOIN users u ON p.user_id = u.id
                                WHERE p.user_id = ?";

                        if (!empty($type)) {
                            $sql .= " AND p.type = ?";
                            $stmt = $db->prepare($sql . " ORDER BY p.created_at DESC");
                            $stmt->execute([$userId, $type]);
                        } else {
                            $stmt = $db->prepare($sql . " ORDER BY p.created_at DESC");
                            $stmt->execute([$userId]);
                        }
                    } else {
                        // Get approved products for general listing
                        $sql = "SELECT p.*, u.full_name as merchant_name FROM products p
                                LEFT JOIN users u ON p.user_id = u.id
                                WHERE p.status = 'approved'";

                        if (!empty($type)) {
                            $sql .= " AND p.type = ?";
                            $stmt = $db->prepare($sql);
                            $stmt->execute([$type]);
                        } else {
                            $stmt = $db->prepare($sql);
                            $stmt->execute();
                        }
                    }

                    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    // Log the query result for debugging
                    error_log("Products query result: " . count($products) . " products found for type: " . ($type ?: 'all'));

                    sendResponse([
                        'success' => true,
                        'products' => $products,
                        'count' => count($products),
                        'type_filter' => $type ?: 'all'
                    ]);

                } catch (PDOException $e) {
                    error_log("Database error in products endpoint: " . $e->getMessage());
                    sendError('Database error occurred', 500, $e->getMessage());
                }

            } elseif ($method === 'POST') {
                $description = $_POST['description'] ?? '';
                $countryOfOrigin = $_POST['country_of_origin'] ?? '';
                $price = $_POST['price'] ?? '';
                $type = $_POST['type'] ?? '';
                $userId = $_POST['user_id'] ?? '';

                if (empty($userId)) {
                    sendError('User ID is required');
                }

                if (empty($description) || empty($countryOfOrigin) || empty($price) || empty($type)) {
                    sendError('All fields are required');
                }

                if (!in_array($type, ['industrial', 'food'])) {
                    sendError('Invalid product type');
                }

                // Handle image upload
                $image = null;
                if (isset($_FILES['image'])) {
                    $image = uploadFile($_FILES['image'], '../uploads/products/');
                }

                // Insert product
                $stmt = $db->prepare("
                    INSERT INTO products (user_id, image, description, country_of_origin, price, type)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");

                if ($stmt->execute([$userId, $image, $description, $countryOfOrigin, $price, $type])) {
                    sendResponse([
                        'success' => true,
                        'message' => 'Product added successfully and is under review'
                    ]);
                } else {
                    sendError('Failed to add product');
                }
            } else {
                sendError('Method not allowed', 405);
            }
            break;

        case 'orders':
            if ($method === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                $productId = $input['product_id'] ?? '';
                $buyerId = $input['buyer_id'] ?? '';

                if (empty($buyerId)) {
                    sendError('Buyer ID is required');
                }

                if (empty($productId)) {
                    sendError('Product ID is required');
                }

                // Check if product exists and is approved
                $stmt = $db->prepare("SELECT * FROM products WHERE id = ? AND status = 'approved'");
                $stmt->execute([$productId]);
                $product = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$product) {
                    sendError('Product not found or not approved');
                }

                // Insert order
                $stmt = $db->prepare("INSERT INTO orders (product_id, buyer_id) VALUES (?, ?)");

                if ($stmt->execute([$productId, $buyerId])) {
                    sendResponse([
                        'success' => true,
                        'message' => 'Order created successfully and is under review'
                    ]);
                } else {
                    sendError('Failed to create order');
                }

            } elseif ($method === 'GET') {
                $userId = $_GET['user_id'] ?? '';
                $merchantId = $_GET['merchant_id'] ?? '';

                if (empty($userId) && empty($merchantId)) {
                    sendError('User ID or Merchant ID is required');
                }

                if (!empty($userId)) {
                    // Get orders for buyer
                    $stmt = $db->prepare("
                        SELECT o.*, p.description as product_description, p.price, p.image as product_image,
                               u.full_name as merchant_name
                        FROM orders o
                        LEFT JOIN products p ON o.product_id = p.id
                        LEFT JOIN users u ON p.user_id = u.id
                        WHERE o.buyer_id = ?
                        ORDER BY o.created_at DESC
                    ");
                    $stmt->execute([$userId]);
                } else {
                    // Get orders for merchant
                    $stmt = $db->prepare("
                        SELECT o.*, p.description as product_description, p.price, p.image as product_image,
                               b.full_name as buyer_name, b.email as buyer_email, b.phone_number as buyer_phone
                        FROM orders o
                        LEFT JOIN products p ON o.product_id = p.id
                        LEFT JOIN users b ON o.buyer_id = b.id
                        WHERE p.user_id = ?
                        ORDER BY o.created_at DESC
                    ");
                    $stmt->execute([$merchantId]);
                }

                $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

                sendResponse([
                    'success' => true,
                    'orders' => $orders
                ]);
            } elseif ($method === 'DELETE') {
                // إلغاء الطلب (فقط للطلبات تحت المراجعة)
                $orderId = $segments[1] ?? '';

                if (empty($orderId)) {
                    sendError('Order ID is required');
                }

                // التحقق من وجود الطلب وحالته
                $stmt = $db->prepare("SELECT * FROM orders WHERE id = ?");
                $stmt->execute([$orderId]);
                $order = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$order) {
                    sendError('Order not found');
                }

                if ($order['status'] !== 'under_review') {
                    sendError('Only orders under review can be cancelled');
                }

                // حذف الطلب
                $stmt = $db->prepare("DELETE FROM orders WHERE id = ?");

                if ($stmt->execute([$orderId])) {
                    sendResponse([
                        'success' => true,
                        'message' => 'Order cancelled successfully'
                    ]);
                } else {
                    sendError('Failed to cancel order');
                }
            } else {
                sendError('Method not allowed', 405);
            }
            break;

        default:
            sendError('Endpoint not found', 404);
    }

} catch (Exception $e) {
    error_log("API Exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    sendError('Internal server error occurred', 500, $e->getMessage());
}
?>
