import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class ApiService {
  static const String baseUrl = 'https://alraid.ridcod.com/api';
  late final Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors for logging
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (object) => debugPrint(object.toString()),
      ));
    }
  }

  // Authentication APIs
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await _dio.post('/login', data: {
        'email': email,
        'password': password,
      });
      return response.data;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  Future<Map<String, dynamic>> register({
    required String fullName,
    required String email,
    required String phoneNumber,
    required String password,
    required String shippingAddress,
    required String country,
    required String role,
    String? commercialRegisterPhoto,
  }) async {
    try {
      FormData formData = FormData.fromMap({
        'full_name': fullName,
        'email': email,
        'phone_number': phoneNumber,
        'password': password,
        'shipping_address': shippingAddress,
        'country': country,
        'role': role,
      });

      if (commercialRegisterPhoto != null &&
          commercialRegisterPhoto.isNotEmpty) {
        formData.files.add(MapEntry(
          'commercial_register_photo',
          await MultipartFile.fromFile(commercialRegisterPhoto),
        ));
      }

      final response = await _dio.post('/register', data: formData);
      return response.data;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  // User APIs
  Future<Map<String, dynamic>> getUserProfile(int userId) async {
    try {
      final response = await _dio.get('/user/$userId');
      return response.data;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  // Product APIs
  Future<Map<String, dynamic>> getProducts({String? type}) async {
    try {
      final queryParams = type != null ? {'type': type} : <String, dynamic>{};
      print('ApiService: Requesting products with params: $queryParams');
      print('ApiService: Full URL: $baseUrl/products');

      final response =
          await _dio.get('/products', queryParameters: queryParams);

      print('ApiService: Response status: ${response.statusCode}');
      print('ApiService: Response data: ${response.data}');

      return response.data;
    } on DioException catch (e) {
      print('ApiService: DioException: ${e.message}');
      print('ApiService: Response: ${e.response?.data}');
      return _handleError(e);
    }
  }

  Future<Map<String, dynamic>> addProduct({
    required int userId,
    required String description,
    required String countryOfOrigin,
    required double price,
    required String type,
    String? imagePath,
    Uint8List? imageBytes,
    String? imageFileName,
  }) async {
    try {
      FormData formData = FormData.fromMap({
        'user_id': userId.toString(),
        'description': description,
        'country_of_origin': countryOfOrigin,
        'price': price.toString(),
        'type': type,
      });

      // Handle image upload - support both file path and bytes
      if (imageBytes != null && imageFileName != null) {
        // For web - use bytes with proper content type
        String contentType = 'image/jpeg';
        final extension = imageFileName.toLowerCase();
        if (extension.endsWith('.png')) {
          contentType = 'image/png';
        } else if (extension.endsWith('.jpg') || extension.endsWith('.jpeg')) {
          contentType = 'image/jpeg';
        }

        formData.files.add(MapEntry(
          'image',
          MultipartFile.fromBytes(
            imageBytes,
            filename: imageFileName,
            contentType: DioMediaType.parse(contentType),
          ),
        ));
      } else if (imagePath != null && imagePath.isNotEmpty) {
        // For mobile - use file path
        formData.files.add(MapEntry(
          'image',
          await MultipartFile.fromFile(imagePath),
        ));
      }

      final response = await _dio.post('/products', data: formData);
      return response.data;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  // Order APIs
  Future<Map<String, dynamic>> createOrder({
    required int productId,
    required int buyerId,
  }) async {
    try {
      final response = await _dio.post('/orders', data: {
        'product_id': productId,
        'buyer_id': buyerId,
      });
      return response.data;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  Future<Map<String, dynamic>> getUserOrders({
    int? userId,
    int? merchantId,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (userId != null) queryParams['user_id'] = userId;
      if (merchantId != null) queryParams['merchant_id'] = merchantId;

      final response = await _dio.get('/orders', queryParameters: queryParams);
      return response.data;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  Future<Map<String, dynamic>> cancelOrder(int orderId) async {
    try {
      final response = await _dio.delete('/orders/$orderId');
      return response.data;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  // Health check
  Future<Map<String, dynamic>> healthCheck() async {
    try {
      final response = await _dio.get('/health');
      return response.data;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  // File upload helper
  Future<String?> uploadImage(String filePath, String endpoint) async {
    try {
      FormData formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(filePath),
      });

      final response = await _dio.post(endpoint, data: formData);
      if (response.data['success'] == true) {
        return response.data['filename'];
      }
      return null;
    } on DioException catch (e) {
      debugPrint('Upload error: ${e.message}');
      return null;
    }
  }

  // Error handler
  Map<String, dynamic> _handleError(DioException error) {
    debugPrint('API Error: ${error.message}');
    debugPrint('Error Type: ${error.type}');
    debugPrint('Error Response: ${error.response?.data}');

    String message = 'An error occurred. Please try again.';

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        message = 'Connection timeout. Please check your internet connection.';
        break;
      case DioExceptionType.badResponse:
        if (error.response?.data is Map) {
          final responseData = error.response!.data as Map<String, dynamic>;
          message = responseData['message'] ?? 'Server error occurred.';
        } else {
          message = 'Server error: ${error.response?.statusCode}';
        }
        break;
      case DioExceptionType.cancel:
        message = 'Request was cancelled.';
        break;
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          message = 'No internet connection. Please check your network.';
        } else {
          message = 'Network error occurred.';
        }
        break;
      default:
        message = 'An unexpected error occurred.';
    }

    return {
      'success': false,
      'message': message,
      'error': error.message,
    };
  }

  // Image compression helper
  static Future<File?> compressImage(File imageFile) async {
    try {
      // Import the compression package
      // final compressedFile = await FlutterImageCompress.compressAndGetFile(
      //   imageFile.absolute.path,
      //   '${imageFile.parent.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg',
      //   quality: 70,
      //   minWidth: 800,
      //   minHeight: 600,
      // );
      // return compressedFile;

      // For now, return the original file
      return imageFile;
    } catch (e) {
      debugPrint('Image compression error: $e');
      return imageFile;
    }
  }

  // Network connectivity check
  static Future<bool> isConnected() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}
