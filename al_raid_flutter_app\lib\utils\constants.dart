class AppConstants {
  // API Configuration
  static const String baseUrl = 'https://alraid.ridcod.com/api';
  static const String uploadsUrl = 'https://alraid.ridcod.com/uploads';

  // App Configuration
  static const String appName = 'AL-Raid';
  static const String appVersion = '1.0.0';
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxImagesPerProduct = 5;

  // Animation Durations
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration fastAnimationDuration = Duration(milliseconds: 200);
  static const Duration slowAnimationDuration = Duration(milliseconds: 500);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double defaultMargin = 16.0;
  static const double defaultBorderRadius = 12.0;
  static const double cardElevation = 4.0;

  // Validation Constants
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;

  // Categories
  static const List<String> productCategories = [
    'Industrial',
    'Food',
    'Electronics',
    'Automotive',
    'Textiles',
    'Chemicals',
    'Machinery',
    'Agriculture',
  ];

  // Order Status
  static const List<String> orderStatuses = [
    'pending',
    'processing',
    'shipped',
    'delivered',
    'cancelled',
  ];

  // Currency
  static const String currency = 'SAR';
  static const String currencySymbol = 'ر.س';

  // Image Paths
  static const String placeholderImagePath = 'assets/images/placeholder.png';
  static const String logoImagePath = 'assets/images/logo.png';

  // Error Messages
  static const String networkErrorMessage =
      'Network error. Please check your connection.';
  static const String serverErrorMessage =
      'Server error. Please try again later.';
  static const String unknownErrorMessage = 'An unknown error occurred.';

  // Success Messages
  static const String loginSuccessMessage = 'Login successful';
  static const String registerSuccessMessage = 'Registration successful';
  static const String productAddedMessage = 'Product added successfully';
  static const String orderPlacedMessage = 'Order placed successfully';
}
