import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';

import '../screens/splash_screen.dart';
import '../screens/login_screen.dart';
import '../screens/registration_screen.dart';
import '../screens/main_navigation_screen.dart';
import '../screens/product_detail_screen.dart';
import '../screens/add_product_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/order_detail_screen.dart';
import '../screens/cart_screen.dart';
import '../models/product.dart';
import '../models/order.dart';

class AppRouter {
  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: '/splash',
      redirect: (context, state) {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final isLoggedIn = authProvider.isAuthenticated;
        final currentPath = state.fullPath;

        // If on splash screen, let it handle its own navigation
        if (currentPath == '/splash') {
          return null;
        }

        // If not logged in and trying to access protected routes
        if (!isLoggedIn && !['/login', '/register'].contains(currentPath)) {
          return '/login';
        }

        // If logged in and trying to access auth routes
        if (isLoggedIn && ['/login', '/register'].contains(currentPath)) {
          return '/home';
        }

        return null;
      },
      routes: [
        GoRoute(
          path: '/splash',
          name: 'splash',
          builder: (context, state) => const SplashScreen(),
        ),
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/register',
          name: 'register',
          builder: (context, state) => const RegistrationScreen(),
        ),
        GoRoute(
          path: '/home',
          name: 'home',
          builder: (context, state) => const MainNavigationScreen(),
        ),
        GoRoute(
          path: '/product/:id',
          name: 'productDetail',
          builder: (context, state) {
            final product = state.extra as Product;
            return ProductDetailScreen(product: product);
          },
        ),
        GoRoute(
          path: '/add-product',
          name: 'addProduct',
          builder: (context, state) => const AddProductScreen(),
        ),
        GoRoute(
          path: '/edit-product/:id',
          name: 'editProduct',
          builder: (context, state) {
            return const AddProductScreen();
          },
        ),
        GoRoute(
          path: '/profile',
          name: 'profile',
          builder: (context, state) => const ProfileScreen(),
        ),
        GoRoute(
          path: '/orders',
          name: 'orders',
          builder: (context, state) => const OrdersScreen(),
        ),
        GoRoute(
          path: '/order/:id',
          name: 'orderDetail',
          builder: (context, state) {
            final orderId = state.pathParameters['id']!;
            final order = state.extra as Order?;
            return OrderDetailScreen(orderId: orderId, order: order);
          },
        ),
      ],
      errorBuilder: (context, state) => Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
                Theme.of(context).colorScheme.surface,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Page not found',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  'The page you are looking for does not exist.',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    final authProvider =
                        Provider.of<AuthProvider>(context, listen: false);
                    if (authProvider.isAuthenticated) {
                      context.go('/home');
                    } else {
                      context.go('/splash');
                    }
                  },
                  icon: const Icon(Icons.home),
                  label: const Text('Go Home'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Navigation helper extensions
extension AppNavigation on BuildContext {
  void pushProduct(Product product) {
    go('/product/${product.id}', extra: product);
  }

  void pushProductById(String productId) {
    go('/product/$productId');
  }

  void pushAddProduct() {
    go('/add-product');
  }

  void pushEditProduct(Product product) {
    go('/edit-product/${product.id}', extra: product);
  }

  void pushOrder(Order order) {
    go('/order/${order.id}', extra: order);
  }

  void pushOrderById(String orderId) {
    go('/order/$orderId');
  }

  void pushOrders() {
    go('/orders');
  }

  void pushCart() {
    go('/cart');
  }

  void pushProfile() {
    go('/profile');
  }

  void pushHome() {
    go('/home');
  }

  void pushLogin() {
    go('/login');
  }

  void pushRegister() {
    go('/register');
  }
}
