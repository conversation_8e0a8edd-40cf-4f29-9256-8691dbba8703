import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../generated/app_localizations.dart';
import 'package:go_router/go_router.dart';
import '../providers/language_provider.dart';
import '../providers/auth_provider.dart';
import '../utils/theme.dart';
import '../widgets/glassmorphic_card.dart';
import '../widgets/gradient_button.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  bool _showTerms = false;
  bool _termsAccepted = false;
  bool _hasCheckedAuth = false;

  @override
  void initState() {
    super.initState();
    _checkAuthAndNavigate();
  }

  void _checkAuthAndNavigate() {
    if (_hasCheckedAuth) return; // Prevent multiple checks

    // Check if user is already authenticated
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && !_hasCheckedAuth) {
        _hasCheckedAuth = true;
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        if (authProvider.isAuthenticated) {
          // User is logged in, go directly to home
          context.go('/home');
        } else {
          // Show terms for new users
          setState(() {
            _showTerms = true;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final l10n = AppLocalizations.of(context);
    final brightness = Theme.of(context).brightness;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: brightness == Brightness.light
              ? AppTheme.primaryGradientDecoration
              : const LinearGradient(
                  colors: [Color(0xFF0F0F23), Color(0xFF1A1A2E)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                // Language Toggle
                Align(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: () => languageProvider.toggleLanguage(),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            languageProvider.isArabic ? 'العربية' : 'English',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.language,
                            color: Colors.white,
                            size: 18,
                          ),
                        ],
                      ),
                    ),
                  ),
                ).animate().fadeIn(delay: 500.ms).slideX(begin: 0.3),

                const Spacer(),

                // App Logo and Name
                Column(
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.storefront_rounded,
                        size: 60,
                        color: AppTheme.primaryColor,
                      ),
                    ).animate().scale(delay: 300.ms, duration: 800.ms),
                    const SizedBox(height: 24),
                    Text(
                      'AL-Raid',
                      style: Theme.of(context).textTheme.displayLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 48,
                          ),
                    ).animate().fadeIn(delay: 600.ms).slideY(begin: 0.3),
                    const SizedBox(height: 8),
                    Text(
                      l10n.appSubtitle,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontWeight: FontWeight.w300,
                          ),
                      textAlign: TextAlign.center,
                    ).animate().fadeIn(delay: 800.ms).slideY(begin: 0.3),
                  ],
                ),

                const Spacer(),

                // Terms and Conditions
                if (_showTerms) ...[
                  GlassmorphicCard(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            l10n.termsAndConditions,
                            style: Theme.of(
                              context,
                            ).textTheme.headlineSmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            l10n.termsText,
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  height: 1.5,
                                ),
                            textAlign: languageProvider.isArabic
                                ? TextAlign.right
                                : TextAlign.left,
                          ),
                          const SizedBox(height: 24),
                          Row(
                            children: [
                              Checkbox(
                                value: _termsAccepted,
                                onChanged: (value) {
                                  setState(() {
                                    _termsAccepted = value ?? false;
                                  });
                                },
                                activeColor: AppTheme.accentColor,
                                checkColor: Colors.white,
                              ),
                              Expanded(
                                child: Text(
                                  l10n.acceptTerms,
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodyMedium?.copyWith(
                                        color:
                                            Colors.white.withValues(alpha: 0.9),
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ).animate().fadeIn(delay: 1000.ms).slideY(begin: 0.3),
                  const SizedBox(height: 24),
                  GradientButton(
                    text: l10n.continueButton,
                    onPressed: _termsAccepted ? _continueToApp : null,
                    gradient: AppTheme.accentGradientDecoration,
                    textStyle: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ).animate().fadeIn(delay: 1200.ms).slideY(begin: 0.3),
                ],

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _continueToApp() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (authProvider.isAuthenticated) {
      context.go('/home');
    } else {
      context.go('/login');
    }
  }
}
