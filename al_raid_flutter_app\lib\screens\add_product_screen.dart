import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import 'package:flutter/foundation.dart';

import '../providers/language_provider.dart';
import '../providers/product_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/glassmorphic_card.dart';
import '../widgets/gradient_button.dart';
import '../widgets/animated_input_field.dart';
import 'merchant_products_screen.dart';

class AddProductScreen extends StatefulWidget {
  const AddProductScreen({super.key});

  @override
  State<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends State<AddProductScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();

  late AnimationController _animationController;
  final ImagePicker _imagePicker = ImagePicker();

  String _selectedCategory = 'industrial';
  List<XFile> _selectedImages = [];
  List<Uint8List> _selectedImagesBytes = []; // For web compatibility
  final List<String> _categories = ['industrial', 'food'];

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty) {
        // Filter only JPEG and PNG images
        final validImages = images.where((image) {
          final extension = image.name.toLowerCase();
          return extension.endsWith('.jpg') ||
              extension.endsWith('.jpeg') ||
              extension.endsWith('.png');
        }).toList();

        if (validImages.isEmpty) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  Provider.of<LanguageProvider>(context, listen: false).isArabic
                      ? 'يرجى اختيار صور JPEG أو PNG فقط'
                      : 'Please select only JPEG or PNG images',
                ),
                backgroundColor: Theme.of(context).colorScheme.error,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            );
          }
          return;
        }

        // Limit to 5 images
        final limitedImages = validImages.take(5).toList();

        // Load image bytes for web compatibility
        final List<Uint8List> imageBytes = [];
        for (final image in limitedImages) {
          final bytes = await image.readAsBytes();

          // Check file size (5MB limit)
          if (bytes.length > 5 * 1024 * 1024) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    Provider.of<LanguageProvider>(context, listen: false)
                            .isArabic
                        ? 'حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت'
                        : 'Image size is too large. Maximum 5MB allowed',
                  ),
                  backgroundColor: Theme.of(context).colorScheme.error,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              );
            }
            continue;
          }

          imageBytes.add(bytes);
        }

        setState(() {
          _selectedImages = limitedImages;
          _selectedImagesBytes = imageBytes;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصور: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
      _selectedImagesBytes.removeAt(index);
    });
  }

  Future<void> _submitProduct() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (_selectedImages.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                Provider.of<LanguageProvider>(context, listen: false).isArabic
                    ? 'يرجى إضافة صورة واحدة على الأقل'
                    : 'Please add at least one image',
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
        return;
      }

      final productProvider =
          Provider.of<ProductProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final languageProvider =
          Provider.of<LanguageProvider>(context, listen: false);

      try {
        // Prepare image data for upload
        String? imagePath;
        Uint8List? imageBytes;
        String? imageFileName;

        if (_selectedImages.isNotEmpty) {
          if (kIsWeb) {
            // For web - use bytes
            imageBytes = _selectedImagesBytes.first;
            imageFileName = _selectedImages.first.name;

            // Ensure filename has proper extension
            if (!imageFileName.toLowerCase().endsWith('.jpg') &&
                !imageFileName.toLowerCase().endsWith('.jpeg') &&
                !imageFileName.toLowerCase().endsWith('.png')) {
              imageFileName = '$imageFileName.jpg';
            }
          } else {
            // For mobile - use file path
            imagePath = _selectedImages.first.path;
          }
        }

        final success = await productProvider.addProduct(
          userId: authProvider.user?.id ?? 0,
          description: _descriptionController.text.trim(),
          countryOfOrigin: 'Unknown',
          price: double.parse(_priceController.text),
          type: _selectedCategory,
          imagePath: imagePath,
          imageBytes: imageBytes,
          imageFileName: imageFileName,
        );

        if (mounted) {
          if (success) {
            // Show confirmation dialog
            _showSuccessDialog(languageProvider);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  languageProvider.isArabic
                      ? 'خطأ في إضافة المنتج: ${productProvider.error ?? "خطأ غير معروف"}'
                      : 'Error adding product: ${productProvider.error ?? "Unknown error"}',
                ),
                backgroundColor: Theme.of(context).colorScheme.error,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                duration: const Duration(seconds: 4),
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                languageProvider.isArabic
                    ? 'خطأ في الاتصال: ${e.toString()}'
                    : 'Connection error: ${e.toString()}',
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isRTL = languageProvider.isArabic;

    return Scaffold(
      backgroundColor:
          isDark ? const Color(0xFF121212) : const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: Text(
          isRTL ? 'إضافة منتج جديد' : 'Add New Product',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: theme.primaryColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            isRTL ? Icons.arrow_forward : Icons.arrow_back,
            color: Colors.white,
          ),
        ),
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _animationController,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Basic Information
                    GlassmorphicCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isRTL ? 'المعلومات الأساسية' : 'Basic Information',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: isDark ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 20),

                          AnimatedInputField(
                            labelText: isRTL ? 'اسم المنتج' : 'Product Name',
                            hintText: isRTL
                                ? 'أدخل اسم المنتج'
                                : 'Enter product name',
                            controller: _nameController,
                            prefixIcon: const Icon(Icons.inventory),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return isRTL
                                    ? 'يرجى إدخال اسم المنتج'
                                    : 'Please enter product name';
                              }
                              return null;
                            },
                            isRequired: true,
                          ),

                          AnimatedInputField(
                            labelText: isRTL ? 'الوصف' : 'Description',
                            hintText: isRTL
                                ? 'أدخل وصف المنتج'
                                : 'Enter product description',
                            controller: _descriptionController,
                            prefixIcon: const Icon(Icons.description),
                            maxLines: 4,
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return isRTL
                                    ? 'يرجى إدخال وصف المنتج'
                                    : 'Please enter product description';
                              }
                              return null;
                            },
                            isRequired: true,
                          ),

                          Row(
                            children: [
                              Expanded(
                                child: AnimatedInputField(
                                  labelText: isRTL ? 'السعر' : 'Price',
                                  hintText: isRTL ? '0.00' : '0.00',
                                  controller: _priceController,
                                  keyboardType: TextInputType.number,
                                  prefixIcon: const Icon(Icons.attach_money),
                                  validator: (value) {
                                    if (value?.isEmpty ?? true) {
                                      return isRTL
                                          ? 'يرجى إدخال السعر'
                                          : 'Please enter price';
                                    }
                                    if (double.tryParse(value!) == null ||
                                        double.parse(value) <= 0) {
                                      return isRTL
                                          ? 'يرجى إدخال سعر صحيح'
                                          : 'Please enter valid price';
                                    }
                                    return null;
                                  },
                                  isRequired: true,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: AnimatedInputField(
                                  labelText: isRTL ? 'المخزون' : 'Stock',
                                  hintText: isRTL ? '0' : '0',
                                  controller: _stockController,
                                  keyboardType: TextInputType.number,
                                  prefixIcon: const Icon(Icons.inventory_2),
                                  validator: (value) {
                                    if (value?.isEmpty ?? true) {
                                      return isRTL
                                          ? 'يرجى إدخال الكمية'
                                          : 'Please enter stock';
                                    }
                                    if (int.tryParse(value!) == null ||
                                        int.parse(value) < 0) {
                                      return isRTL
                                          ? 'يرجى إدخال كمية صحيحة'
                                          : 'Please enter valid stock';
                                    }
                                    return null;
                                  },
                                  isRequired: true,
                                ),
                              ),
                            ],
                          ),

                          // Category Selection
                          Text(
                            isRTL ? 'الفئة' : 'Category',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: isDark
                                  ? Colors.white70
                                  : Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(height: 8),

                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.grey.shade300,
                              ),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: _selectedCategory,
                                isExpanded: true,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedCategory = value!;
                                  });
                                },
                                items: _categories.map((category) {
                                  String categoryText;
                                  switch (category) {
                                    case 'industrial':
                                      categoryText =
                                          isRTL ? 'صناعي' : 'Industrial';
                                      break;
                                    case 'food':
                                      categoryText = isRTL ? 'غذائي' : 'Food';
                                      break;
                                    case 'electronics':
                                      categoryText =
                                          isRTL ? 'إلكترونيات' : 'Electronics';
                                      break;
                                    case 'clothing':
                                      categoryText =
                                          isRTL ? 'ملابس' : 'Clothing';
                                      break;
                                    default:
                                      categoryText = category;
                                  }

                                  return DropdownMenuItem<String>(
                                    value: category,
                                    child: Text(categoryText),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                        .animate()
                        .fadeIn(delay: 200.ms, duration: 600.ms)
                        .slideY(begin: 0.2, end: 0),

                    const SizedBox(height: 20),

                    // Product Images
                    GlassmorphicCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                isRTL ? 'صور المنتج' : 'Product Images',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: isDark ? Colors.white : Colors.black87,
                                ),
                              ),
                              GradientButton(
                                text: isRTL ? 'إضافة صور' : 'Add Images',
                                onPressed: _pickImages,
                                height: 40,
                                icon: const Icon(
                                  Icons.add_photo_alternate,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (_selectedImages.isEmpty)
                            Container(
                              height: 120,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.grey.shade300,
                                  style: BorderStyle.solid,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add_photo_alternate,
                                      size: 48,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      isRTL
                                          ? 'اضغط لإضافة صور'
                                          : 'Tap to add images',
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          else
                            SizedBox(
                              height: 120,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: _selectedImages.length,
                                itemBuilder: (context, index) {
                                  return Container(
                                    width: 100,
                                    margin: const EdgeInsets.only(right: 12),
                                    child: Stack(
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: kIsWeb
                                              ? Image.memory(
                                                  _selectedImagesBytes[index],
                                                  width: 100,
                                                  height: 120,
                                                  fit: BoxFit.cover,
                                                )
                                              : Image.file(
                                                  File(_selectedImages[index]
                                                      .path),
                                                  width: 100,
                                                  height: 120,
                                                  fit: BoxFit.cover,
                                                ),
                                        ),
                                        Positioned(
                                          top: 4,
                                          right: 4,
                                          child: GestureDetector(
                                            onTap: () => _removeImage(index),
                                            child: Container(
                                              width: 24,
                                              height: 24,
                                              decoration: const BoxDecoration(
                                                color: Colors.red,
                                                shape: BoxShape.circle,
                                              ),
                                              child: const Icon(
                                                Icons.close,
                                                size: 16,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          const SizedBox(height: 8),
                          Text(
                            isRTL
                                ? 'يمكنك إضافة حتى 5 صور'
                                : 'You can add up to 5 images',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    )
                        .animate()
                        .fadeIn(delay: 400.ms, duration: 600.ms)
                        .slideY(begin: 0.2, end: 0),

                    const SizedBox(height: 30),

                    // Submit Button
                    Consumer<ProductProvider>(
                      builder: (context, productProvider, child) {
                        return GradientButton(
                          text: isRTL ? 'إضافة المنتج' : 'Add Product',
                          onPressed: _submitProduct,
                          isLoading: productProvider.isLoading,
                          width: double.infinity,
                          height: 56,
                          icon: const Icon(
                            Icons.add_business,
                            color: Colors.white,
                            size: 24,
                          ),
                        );
                      },
                    )
                        .animate()
                        .fadeIn(delay: 800.ms, duration: 600.ms)
                        .slideY(begin: 0.2, end: 0),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showSuccessDialog(LanguageProvider languageProvider) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                languageProvider.isArabic ? 'تم بنجاح!' : 'Success!',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
            ],
          ),
          content: Text(
            languageProvider.isArabic
                ? 'تم إرسال المنتج بنجاح وهو الآن قيد المراجعة من قبل الإدارة. ستتمكن من متابعة حالة المنتج في صفحة منتجاتي.'
                : 'Product is under review by the administration. You can track the status of your product in the My Products page.',
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog

                // Clear form
                _nameController.clear();
                _descriptionController.clear();
                _priceController.clear();
                _stockController.clear();
                setState(() {
                  _selectedImages.clear();
                  _selectedImagesBytes.clear();
                  _selectedCategory = 'industrial';
                });

                // Navigate to merchant products screen
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const MerchantProductsScreen(),
                  ),
                );
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Text(
                  languageProvider.isArabic
                      ? 'عرض منتجاتي'
                      : 'View My Products',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
