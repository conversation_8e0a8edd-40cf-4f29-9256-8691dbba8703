<?php
// Test script to verify merchant products API endpoint
header('Content-Type: application/json');

// Test the merchant products endpoint
$baseUrl = 'https://alraid.ridcod.com/api';

// Test 1: Get all products (should work)
echo "=== Test 1: Get all products ===\n";
$response1 = file_get_contents($baseUrl . '/products');
echo $response1 . "\n\n";

// Test 2: Get products for a specific merchant (user_id=1)
echo "=== Test 2: Get merchant products (user_id=1) ===\n";
$response2 = file_get_contents($baseUrl . '/products?user_id=1');
echo $response2 . "\n\n";

// Test 3: Get industrial products for a specific merchant
echo "=== Test 3: Get merchant industrial products (user_id=1) ===\n";
$response3 = file_get_contents($baseUrl . '/products?user_id=1&type=industrial');
echo $response3 . "\n\n";

// Test 4: Get food products for a specific merchant
echo "=== Test 4: Get merchant food products (user_id=1) ===\n";
$response4 = file_get_contents($baseUrl . '/products?user_id=1&type=food');
echo $response4 . "\n\n";

echo "=== API Tests Complete ===\n";
?>
