import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/product.dart';
import '../services/api_service.dart';

class ProductProvider with ChangeNotifier {
  List<Product> _products = [];
  List<Product> _filteredProducts = [];
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';
  String? _typeFilter;

  List<Product> get products => _filteredProducts;
  List<Product> get allProducts => _products;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get searchQuery => _searchQuery;
  String? get typeFilter => _typeFilter;

  final ApiService _apiService = ApiService();

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<void> loadProducts({String? type}) async {
    // تحديد قيمة افتراضية إذا لم يتم تحديد نوع
    final productType = type ?? 'all';
    debugPrint(
        'ProductProvider: Starting to load products with type: $productType');
    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.getProducts(type: type);
      debugPrint('ProductProvider: API result: $result');

      if (result['success'] == true) {
        final productsData = result['products'] as List;
        print('ProductProvider: Found ${productsData.length} products');

        _products = productsData.map((productData) {
          print('ProductProvider: Processing product: $productData');
          try {
            return Product.fromMap(productData);
          } catch (e) {
            print('ProductProvider: Error parsing product: $e');
            print('ProductProvider: Product data: $productData');
            // إرجاع منتج افتراضي في حالة الخطأ
            return Product(
              id: productData['id'] ?? 0,
              userId: productData['user_id'] ?? 0,
              description: productData['description'] ?? 'Unknown Product',
              countryOfOrigin: productData['country_of_origin'] ?? 'Unknown',
              price: 0.0,
              type: productData['type'] ?? 'industrial',
              status: productData['status'] ?? 'approved',
              createdAt: DateTime.now(),
              merchantName: productData['merchant_name'],
              name: productData['description'] ?? 'Unknown Product',
              category: productData['type'] ?? 'Unknown Category',
              stock: 10,
            );
          }
        }).toList();

        print('ProductProvider: Converted ${_products.length} products');
        _applyFilters();
        print(
            'ProductProvider: After filtering: ${_filteredProducts.length} products');
        _setLoading(false);
      } else {
        final errorMsg = result['message'] ?? 'Failed to load products';
        print('ProductProvider: API error: $errorMsg');
        _setError(errorMsg);
        _setLoading(false);
      }
    } catch (e) {
      final errorMsg = 'Network error: ${e.toString()}';
      print('ProductProvider: Exception: $errorMsg');
      _setError(errorMsg);
      _setLoading(false);
    }
  }

  Future<bool> addProduct({
    required int userId,
    required String description,
    required String countryOfOrigin,
    required double price,
    required String type,
    String? imagePath,
    Uint8List? imageBytes,
    String? imageFileName,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.addProduct(
        userId: userId,
        description: description,
        countryOfOrigin: countryOfOrigin,
        price: price,
        type: type,
        imagePath: imagePath,
        imageBytes: imageBytes,
        imageFileName: imageFileName,
      );

      if (result['success'] == true) {
        // Reload products to include the new one
        await loadProducts(type: _typeFilter);
        _setLoading(false);
        return true;
      } else {
        _setError(result['message'] ?? 'Failed to add product');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  void searchProducts(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  // Get merchant-specific products
  Future<List<Product>> getMerchantProducts({
    required int userId,
    String? type,
  }) async {
    try {
      final result = await _apiService.getMerchantProducts(
        userId: userId,
        type: type,
      );

      if (result['success'] == true) {
        final List<dynamic> productsData = result['data'] ?? [];
        return productsData.map((data) => Product.fromMap(data)).toList();
      } else {
        throw Exception(
            result['message'] ?? 'Failed to load merchant products');
      }
    } catch (e) {
      throw Exception('Network error: ${e.toString()}');
    }
  }

  void filterByType(String? type) {
    _typeFilter = type;
    _applyFilters();
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _typeFilter = null;
    _applyFilters();
    notifyListeners();
  }

  void _applyFilters() {
    _filteredProducts = _products.where((product) {
      bool matchesSearch = _searchQuery.isEmpty ||
          product.description
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          product.countryOfOrigin
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          (product.merchantName
                  ?.toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ??
              false);

      bool matchesType = _typeFilter == null || product.type == _typeFilter;

      return matchesSearch && matchesType;
    }).toList();
  }

  List<Product> getProductsByType(String type) {
    return _products.where((product) => product.type == type).toList();
  }

  List<Product> getIndustrialProducts() {
    return getProductsByType('industrial');
  }

  List<Product> getFoodProducts() {
    return getProductsByType('food');
  }

  Product? getProductById(int id) {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  Future<void> refreshProducts() async {
    await loadProducts(type: _typeFilter);
  }

  int get totalProducts => _products.length;
  int get industrialProductsCount =>
      _products.where((p) => p.type == 'industrial').length;
  int get foodProductsCount => _products.where((p) => p.type == 'food').length;
}
